#!/usr/bin/env python3
"""
Allocation and Container Generator Script

This script generates allocation data and containers for a warehouse management system.
It creates allocations and then generates containers in chunks to reach 100,000 total cartons.
"""

import random
import requests
import json
from collections import defaultdict
from typing import Dict, List, Tuple
import time


class AllocationGenerator:
    def __init__(self, base_url: str = "http://localhost:8080", target_cartons: int = 100000, chunk_size: int = 100):
        self.base_url = base_url
        self.wave = random.randint(1, 1000)  # Same wave for all allocations
        self.sku_demand = defaultdict(int)  # Track total demand per SKU
        self.sku_container_supply = defaultdict(int)  # Track total supply per SKU
        self.load_unit_counter = 1
        self.target_cartons = target_cartons
        self.chunk_size = chunk_size
        self.total_allocations_created = 0
        self.total_containers_created = 0
        
    def generate_sku(self) -> str:
        """Generate a 16-digit SKU starting with 4"""
        # Generate 15 random digits after the initial '4'
        remaining_digits = ''.join([str(random.randint(0, 9)) for _ in range(15)])
        return f"4{remaining_digits}"
    
    def generate_load_unit(self) -> str:
        """Generate a 20-digit load unit starting with 02"""
        load_unit = f"02{self.load_unit_counter:018d}"
        self.load_unit_counter += 1
        return load_unit
    
    def create_allocation(self, sku: str, item_count: int, customer: int) -> bool:
        """Create an allocation via POST request"""
        url = f"{self.base_url}/ross/resources/v1.0/testingService/createAllocationForCustomer/{self.wave}/{sku}/{item_count}/{customer}"
        
        try:
            response = requests.post(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ Created allocation: Wave={self.wave}, SKU={sku}, Items={item_count}, Customer={customer}")
                return True
            else:
                print(f"✗ Failed to create allocation: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error creating allocation: {e}")
            return False
    
    def create_container(self, load_unit: str, item_count: int, sku: str) -> bool:
        """Create a container via POST request"""
        url = f"{self.base_url}/ross/resources/v1.0/testingService/createInboundCarton/0{load_unit}/{item_count}/{self.wave}/{sku}"

        try:
            response = requests.post(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ Created container: LoadUnit=0{load_unit}, Items={item_count}, SKU={sku}")
                return True
            else:
                print(f"✗ Failed to create container: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error creating container: {e}")
            return False
    
    def generate_allocation_chunk(self, num_allocations: int) -> int:
        """Generate a chunk of allocations and return number of successful creations"""
        successful_allocations = 0

        for _ in range(num_allocations):
            sku = self.generate_sku()
            item_count = random.randint(1, 10)
            customer = random.randint(1, 100)

            if self.create_allocation(sku, item_count, customer):
                self.sku_demand[sku] += item_count
                successful_allocations += 1
                self.total_allocations_created += 1

            # Small delay to avoid overwhelming the server
            time.sleep(0.05)  # Reduced delay for faster processing

        return successful_allocations
    def generate_container_chunk(self, target_containers: int) -> int:
        """Generate a chunk of containers and return number created"""
        containers_created = 0
        skus_to_process = list(self.sku_demand.keys())

        while containers_created < target_containers and skus_to_process:
            # Pick a random SKU that still needs containers
            sku = random.choice(skus_to_process)
            total_demand = self.sku_demand[sku]
            current_supply = self.sku_container_supply[sku]
            required_supply = int(total_demand * 1.05)

            if current_supply >= required_supply:
                # This SKU is fully supplied, remove from processing list
                skus_to_process.remove(sku)
                continue

            # Generate a container for this SKU
            load_unit = self.generate_load_unit()
            remaining_need = required_supply - current_supply
            container_items = min(random.randint(1, 5), remaining_need)

            if self.create_container(load_unit, container_items, sku):
                self.sku_container_supply[sku] += container_items
                containers_created += 1
                self.total_containers_created += 1

            # Small delay to avoid overwhelming the server
            time.sleep(0.05)  # Reduced delay for faster processing

        return containers_created
        
    def generate_chunked_data(self) -> None:
        """Generate allocations and containers in chunks to reach target cartons"""
        print(f"Generating data to reach {self.target_cartons:,} cartons in chunks of {self.chunk_size}")
        print(f"Wave ID: {self.wave}")
        print("=" * 80)

        # Estimate allocations needed (assuming average 5 items per allocation and 3 items per container)
        estimated_allocations_per_chunk = max(self.chunk_size // 2, 10)  # Generate more allocations to ensure enough demand

        chunk_number = 1

        while self.total_containers_created < self.target_cartons:
            remaining_cartons = self.target_cartons - self.total_containers_created
            cartons_this_chunk = min(self.chunk_size, remaining_cartons)

            print(f"\n--- CHUNK {chunk_number} ---")
            print(f"Target cartons this chunk: {cartons_this_chunk}")
            print(f"Progress: {self.total_containers_created:,}/{self.target_cartons:,} cartons ({self.total_containers_created/self.target_cartons*100:.1f}%)")

            # Generate allocations for this chunk
            print(f"\nGenerating allocations...")
            allocations_created = self.generate_allocation_chunk(estimated_allocations_per_chunk)
            print(f"Created {allocations_created} allocations")

            # Generate containers for this chunk
            print(f"Generating {cartons_this_chunk} containers...")
            containers_created = self.generate_container_chunk(cartons_this_chunk)
            print(f"Created {containers_created} containers")

            chunk_number += 1

            # Progress update
            if chunk_number % 10 == 0:  # Every 10 chunks
                self.print_progress_summary()

        print(f"\n🎉 COMPLETED! Generated {self.total_containers_created:,} cartons total")

    def print_progress_summary(self) -> None:
        """Print a progress summary"""
        print(f"\n--- PROGRESS UPDATE ---")
        print(f"Total allocations created: {self.total_allocations_created:,}")
        print(f"Total containers created: {self.total_containers_created:,}")
        print(f"Total SKUs: {len(self.sku_demand):,}")
        print(f"Total demand: {sum(self.sku_demand.values()):,}")
        print(f"Total supply: {sum(self.sku_container_supply.values()):,}")

    def print_summary(self) -> None:
        """Print a summary of generated data"""
        print("\n" + "=" * 80)
        print("FINAL SUMMARY")
        print("=" * 80)
        print(f"Wave ID: {self.wave}")
        print(f"Total allocations created: {self.total_allocations_created:,}")
        print(f"Total containers created: {self.total_containers_created:,}")
        print(f"Total SKUs: {len(self.sku_demand):,}")
        print(f"Total allocations demand: {sum(self.sku_demand.values()):,}")
        print(f"Total container supply: {sum(self.sku_container_supply.values()):,}")

        # Show sample of SKUs (first 10)
        print(f"\nSample SKU Coverage (showing first 10 of {len(self.sku_demand)} SKUs):")
        print("-" * 60)
        for i, sku in enumerate(sorted(self.sku_demand.keys())[:10]):
            demand = self.sku_demand[sku]
            supply = self.sku_container_supply[sku]
            coverage = (supply / demand * 100) if demand > 0 else 0
            print(f"SKU {sku}: Demand={demand:3d}, Supply={supply:3d} ({coverage:5.1f}%)")

        if len(self.sku_demand) > 10:
            print(f"... and {len(self.sku_demand) - 10:,} more SKUs")


def main():
    """Main function to run the allocation and container generation"""
    print("Large Scale Allocation and Container Generator")
    print("=" * 80)

    # Initialize the generator for 100,000 cartons in chunks of 100
    generator = AllocationGenerator(target_cartons=100000, chunk_size=100)

    try:
        # Generate allocations and containers in chunks
        generator.generate_chunked_data()

        # Print final summary
        generator.print_summary()

    except KeyboardInterrupt:
        print("\n\n⚠️  Process interrupted by user")
        print(f"Progress: {generator.total_containers_created:,} containers created so far")
        generator.print_summary()
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print(f"Progress: {generator.total_containers_created:,} containers created so far")


if __name__ == "__main__":
    main()
