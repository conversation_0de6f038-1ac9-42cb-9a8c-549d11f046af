#!/usr/bin/env python3
"""
Allocation and Container Generator Script

This script generates allocation data and containers for a warehouse management system.
It creates allocations and then generates containers in chunks to reach 100,000 total cartons.
"""

import random
import requests
import json
from collections import defaultdict
from typing import Dict, List, Tuple, Set
import time
import os


class AllocationGenerator:
    def __init__(self, base_url: str = "http://localhost:8080", target_cartons: int = 100000, chunk_size: int = 100):
        self.base_url = base_url
        self.wave = random.randint(1, 1000)  # Same wave for all allocations
        self.sku_demand = defaultdict(int)  # Track total demand per SKU
        self.sku_container_supply = defaultdict(int)  # Track total supply per SKU
        self.load_unit_counter = 1
        self.target_cartons = target_cartons
        self.chunk_size = chunk_size
        self.total_allocations_created = 0
        self.total_containers_created = 0

        # File tracking - Global files for cross-wave uniqueness
        self.container_ids_file = "container_ids_global.txt"
        self.sku_ids_file = f"sku_ids_wave_{self.wave}.txt"
        self.used_container_ids = set()
        self.used_sku_ids = set()

        # Load existing IDs if files exist
        self._load_existing_ids()

        # Initialize files
        self._initialize_files()

    def _load_existing_ids(self) -> None:
        """Load existing container and SKU IDs from files"""
        # Load container IDs
        if os.path.exists(self.container_ids_file):
            with open(self.container_ids_file, 'r') as f:
                for line in f:
                    container_id = line.strip()
                    if container_id:
                        self.used_container_ids.add(container_id)
                        # Update counter to avoid duplicates
                        if container_id.startswith('02'):
                            try:
                                counter = int(container_id[2:])
                                self.load_unit_counter = max(self.load_unit_counter, counter + 1)
                            except ValueError:
                                pass

        # Load SKU IDs
        if os.path.exists(self.sku_ids_file):
            with open(self.sku_ids_file, 'r') as f:
                for line in f:
                    sku_id = line.strip()
                    if sku_id:
                        self.used_sku_ids.add(sku_id)

    def _initialize_files(self) -> None:
        """Initialize or create the tracking files"""
        # Create global container IDs file if it doesn't exist
        if not os.path.exists(self.container_ids_file):
            with open(self.container_ids_file, 'w') as f:
                f.write("# Global Container IDs - Unique Across All Waves\n")
                f.write(f"# Last updated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("# Format: 20-digit load units starting with '02'\n")
                f.write("# NEVER REUSE THESE IDs\n\n")
        else:
            # Update timestamp in existing file
            with open(self.container_ids_file, 'a') as f:
                f.write(f"\n# Wave {self.wave} started: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

        # Create SKU IDs file if it doesn't exist
        if not os.path.exists(self.sku_ids_file):
            with open(self.sku_ids_file, 'w') as f:
                f.write(f"# SKU IDs for Wave {self.wave}\n")
                f.write(f"# Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("# Format: 16-digit SKUs starting with '4'\n")
                f.write("# These can be reused across waves\n\n")

    def _write_container_id(self, container_id: str) -> None:
        """Write a container ID to the global tracking file"""
        # Use append mode for thread safety across multiple processes
        with open(self.container_ids_file, 'a') as f:
            f.write(f"{container_id}\n")
            f.flush()  # Ensure immediate write to disk
        self.used_container_ids.add(container_id)

    def _write_sku_id(self, sku_id: str) -> None:
        """Write a SKU ID to the tracking file (only if new)"""
        if sku_id not in self.used_sku_ids:
            with open(self.sku_ids_file, 'a') as f:
                f.write(f"{sku_id}\n")
            self.used_sku_ids.add(sku_id)

    def generate_sku(self) -> str:
        """Generate a 16-digit SKU starting with 4 (can reuse existing SKUs)"""
        # 30% chance to reuse an existing SKU if we have any
        if self.used_sku_ids and random.random() < 0.3:
            sku = random.choice(list(self.used_sku_ids))
        else:
            # Generate new SKU
            remaining_digits = ''.join([str(random.randint(0, 9)) for _ in range(15)])
            sku = f"4{remaining_digits}"
            # Write to file if it's new
            self._write_sku_id(sku)

        return sku

    def generate_load_unit(self) -> str:
        """Generate a unique 20-digit load unit starting with 02"""
        while True:
            load_unit = f"02{self.load_unit_counter:018d}"
            self.load_unit_counter += 1

            # Ensure uniqueness (should always be unique with counter, but double-check)
            if load_unit not in self.used_container_ids:
                self._write_container_id(load_unit)
                return load_unit
    
    def create_allocation(self, sku: str, item_count: int, customer: int) -> bool:
        """Create an allocation via POST request"""
        url = f"{self.base_url}/ross/resources/v1.0/testingService/createAllocationForCustomer/{self.wave}/{sku}/{item_count}/{customer}"
        
        try:
            response = requests.post(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ Created allocation: Wave={self.wave}, SKU={sku}, Items={item_count}, Customer={customer}")
                return True
            else:
                print(f"✗ Failed to create allocation: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error creating allocation: {e}")
            return False
    
    def create_container(self, load_unit: str, item_count: int, sku: str) -> bool:
        """Create a container via POST request"""
        url = f"{self.base_url}/ross/resources/v1.0/testingService/createInboundCarton/{load_unit}/{item_count}/{self.wave}/{sku}"

        try:
            response = requests.post(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ Created container: LoadUnit={load_unit}, Items={item_count}, SKU={sku}")
                return True
            else:
                print(f"✗ Failed to create container: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error creating container: {e}")
            return False
    
    def generate_allocation_chunk(self, num_allocations: int) -> int:
        """Generate a chunk of allocations and return number of successful creations"""
        successful_allocations = 0

        for _ in range(num_allocations):
            sku = self.generate_sku()
            item_count = random.randint(1, 10)
            customer = random.randint(1, 100)

            if self.create_allocation(sku, item_count, customer):
                self.sku_demand[sku] += item_count
                successful_allocations += 1
                self.total_allocations_created += 1

            # Small delay to avoid overwhelming the server
            time.sleep(0.05)  # Reduced delay for faster processing

        return successful_allocations
    def generate_container_chunk(self, target_containers: int) -> int:
        """Generate a chunk of containers and return number created"""
        containers_created = 0
        skus_to_process = list(self.sku_demand.keys())

        while containers_created < target_containers and skus_to_process:
            # Pick a random SKU that still needs containers
            sku = random.choice(skus_to_process)
            total_demand = self.sku_demand[sku]
            current_supply = self.sku_container_supply[sku]
            required_supply = int(total_demand * 1.05)

            if current_supply >= required_supply:
                # This SKU is fully supplied, remove from processing list
                skus_to_process.remove(sku)
                continue

            # Generate a container for this SKU
            load_unit = self.generate_load_unit()
            remaining_need = required_supply - current_supply
            container_items = min(random.randint(1, 5), remaining_need)

            if self.create_container(load_unit, container_items, sku):
                self.sku_container_supply[sku] += container_items
                containers_created += 1
                self.total_containers_created += 1

            # Small delay to avoid overwhelming the server
            time.sleep(0.05)  # Reduced delay for faster processing

        return containers_created
        
    def generate_chunked_data(self) -> None:
        """Generate allocations and containers in chunks to reach target cartons"""
        print(f"Generating data to reach {self.target_cartons:,} cartons in chunks of {self.chunk_size}")
        print(f"Wave ID: {self.wave}")
        print("=" * 80)

        # Estimate allocations needed (assuming average 5 items per allocation and 3 items per container)
        estimated_allocations_per_chunk = max(self.chunk_size // 2, 10)  # Generate more allocations to ensure enough demand

        chunk_number = 1

        while self.total_containers_created < self.target_cartons:
            remaining_cartons = self.target_cartons - self.total_containers_created
            cartons_this_chunk = min(self.chunk_size, remaining_cartons)

            print(f"\n--- CHUNK {chunk_number} ---")
            print(f"Target cartons this chunk: {cartons_this_chunk}")
            print(f"Progress: {self.total_containers_created:,}/{self.target_cartons:,} cartons ({self.total_containers_created/self.target_cartons*100:.1f}%)")

            # Generate allocations for this chunk
            print(f"\nGenerating allocations...")
            allocations_created = self.generate_allocation_chunk(estimated_allocations_per_chunk)
            print(f"Created {allocations_created} allocations")

            # Generate containers for this chunk
            print(f"Generating {cartons_this_chunk} containers...")
            containers_created = self.generate_container_chunk(cartons_this_chunk)
            print(f"Created {containers_created} containers")

            chunk_number += 1

            # Progress update
            if chunk_number % 10 == 0:  # Every 10 chunks
                self.print_progress_summary()

        print(f"\n🎉 COMPLETED! Generated {self.total_containers_created:,} cartons total")

    def print_progress_summary(self) -> None:
        """Print a progress summary"""
        print(f"\n--- PROGRESS UPDATE ---")
        print(f"Total allocations created: {self.total_allocations_created:,}")
        print(f"Total containers created: {self.total_containers_created:,}")
        print(f"Total unique SKUs used: {len(self.used_sku_ids):,}")
        print(f"Total unique container IDs: {len(self.used_container_ids):,}")
        print(f"Total demand: {sum(self.sku_demand.values()):,}")
        print(f"Total supply: {sum(self.sku_container_supply.values()):,}")
        print(f"Files: {self.container_ids_file}, {self.sku_ids_file}")

    def print_summary(self) -> None:
        """Print a summary of generated data"""
        print("\n" + "=" * 80)
        print("FINAL SUMMARY")
        print("=" * 80)
        print(f"Wave ID: {self.wave}")
        print(f"Total allocations created: {self.total_allocations_created:,}")
        print(f"Total containers created: {self.total_containers_created:,}")
        print(f"Total unique SKUs used: {len(self.used_sku_ids):,}")
        print(f"Total unique container IDs used: {len(self.used_container_ids):,}")
        print(f"Total allocations demand: {sum(self.sku_demand.values()):,}")
        print(f"Total container supply: {sum(self.sku_container_supply.values()):,}")

        # File information
        print(f"\nOutput Files:")
        print(f"Container IDs (Global): {self.container_ids_file}")
        print(f"SKU IDs (Wave {self.wave}): {self.sku_ids_file}")
        print(f"⚠️  Container IDs are globally unique across ALL waves")

        # Show sample of SKUs (first 10)
        print(f"\nSample SKU Coverage (showing first 10 of {len(self.sku_demand)} SKUs):")
        print("-" * 60)
        for i, sku in enumerate(sorted(self.sku_demand.keys())[:10]):
            demand = self.sku_demand[sku]
            supply = self.sku_container_supply[sku]
            coverage = (supply / demand * 100) if demand > 0 else 0
            print(f"SKU {sku}: Demand={demand:3d}, Supply={supply:3d} ({coverage:5.1f}%)")

        if len(self.sku_demand) > 10:
            print(f"... and {len(self.sku_demand) - 10:,} more SKUs")


def main():
    """Main function to run the allocation and container generation"""
    print("Large Scale Allocation and Container Generator")
    print("=" * 80)

    # Initialize the generator for 5 million cartons in chunks of 100
    generator = AllocationGenerator(target_cartons=5000000, chunk_size=100)

    try:
        # Generate allocations and containers in chunks
        generator.generate_chunked_data()

        # Print final summary
        generator.print_summary()

    except KeyboardInterrupt:
        print("\n\n⚠️  Process interrupted by user")
        print(f"Progress: {generator.total_containers_created:,} containers created so far")
        generator.print_summary()
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print(f"Progress: {generator.total_containers_created:,} containers created so far")


if __name__ == "__main__":
    main()
