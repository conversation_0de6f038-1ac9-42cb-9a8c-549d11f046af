#!/usr/bin/env python3
"""
Allocation and Container Generator Script

This script generates allocation data and containers for a warehouse management system.
It creates 100 allocations and then generates enough containers to fulfill demand + 5%.
"""

import random
import requests
import json
from collections import defaultdict
from typing import Dict, List, Tuple
import time


class AllocationGenerator:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.wave = random.randint(1, 1000)  # Same wave for all allocations
        self.sku_demand = defaultdict(int)  # Track total demand per SKU
        self.sku_container_supply = defaultdict(int)  # Track total supply per SKU
        self.load_unit_counter = 1
        
    def generate_sku(self) -> str:
        """Generate a 16-digit SKU starting with 4"""
        # Generate 15 random digits after the initial '4'
        remaining_digits = ''.join([str(random.randint(0, 9)) for _ in range(15)])
        return f"4{remaining_digits}"
    
    def generate_load_unit(self) -> str:
        """Generate a 20-digit load unit starting with 02"""
        load_unit = f"02{self.load_unit_counter:018d}"
        self.load_unit_counter += 1
        return load_unit
    
    def create_allocation(self, sku: str, item_count: int, customer: int) -> bool:
        """Create an allocation via POST request"""
        url = f"{self.base_url}/ross/resources/v1.0/testingService/createAllocationForCustomer/{self.wave}/{sku}/{item_count}/{customer}"
        
        try:
            response = requests.post(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ Created allocation: Wave={self.wave}, SKU={sku}, Items={item_count}, Customer={customer}")
                return True
            else:
                print(f"✗ Failed to create allocation: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error creating allocation: {e}")
            return False
    
    def create_container(self, load_unit: str, item_count: int, sku: str) -> bool:
        """Create a container via POST request"""
        url = f"{self.base_url}/ross/resources/v1.0/testingService/createInboundCarton/{load_unit}/{item_count}/{self.wave}/{sku}"
        
        try:
            response = requests.post(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ Created container: LoadUnit={load_unit}, Items={item_count}, SKU={sku}")
                return True
            else:
                print(f"✗ Failed to create container: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error creating container: {e}")
            return False
    
    def generate_allocations(self, num_allocations: int = 100) -> None:
        """Generate the specified number of allocations"""
        print(f"Generating {num_allocations} allocations for wave {self.wave}...")
        print("-" * 60)
        
        successful_allocations = 0
        
        for i in range(num_allocations):
            sku = self.generate_sku()
            item_count = random.randint(1, 10)
            customer = random.randint(1, 100)
            
            if self.create_allocation(sku, item_count, customer):
                self.sku_demand[sku] += item_count
                successful_allocations += 1
            
            # Small delay to avoid overwhelming the server
            time.sleep(0.1)
        
        print(f"\nCompleted allocations: {successful_allocations}/{num_allocations}")
        print(f"Total SKUs with demand: {len(self.sku_demand)}")
        
    def generate_containers(self) -> None:
        """Generate containers to fulfill demand + 5%"""
        print("\nGenerating containers to fulfill demand + 5%...")
        print("-" * 60)
        
        total_containers = 0
        
        for sku, total_demand in self.sku_demand.items():
            # Calculate required supply (demand + 5%)
            required_supply = int(total_demand * 1.05)
            current_supply = 0
            
            print(f"\nSKU {sku}: Demand={total_demand}, Required={required_supply}")
            
            # Generate containers until we meet the required supply
            while current_supply < required_supply:
                load_unit = self.generate_load_unit()
                # Container can hold 1-5 items
                container_items = min(random.randint(1, 5), required_supply - current_supply)
                
                if self.create_container(load_unit, container_items, sku):
                    current_supply += container_items
                    self.sku_container_supply[sku] += container_items
                    total_containers += 1
                
                # Small delay to avoid overwhelming the server
                time.sleep(0.1)
            
            print(f"  Generated supply: {current_supply} items in containers")
        
        print(f"\nTotal containers created: {total_containers}")
        
    def print_summary(self) -> None:
        """Print a summary of generated data"""
        print("\n" + "=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"Wave ID: {self.wave}")
        print(f"Total SKUs: {len(self.sku_demand)}")
        print(f"Total allocations demand: {sum(self.sku_demand.values())}")
        print(f"Total container supply: {sum(self.sku_container_supply.values())}")
        
        print("\nDemand vs Supply by SKU:")
        print("-" * 40)
        for sku in sorted(self.sku_demand.keys()):
            demand = self.sku_demand[sku]
            supply = self.sku_container_supply[sku]
            coverage = (supply / demand * 100) if demand > 0 else 0
            print(f"SKU {sku}: Demand={demand:3d}, Supply={supply:3d} ({coverage:5.1f}%)")


def main():
    """Main function to run the allocation and container generation"""
    print("Allocation and Container Generator")
    print("=" * 60)
    
    # Initialize the generator
    generator = AllocationGenerator()
    
    try:
        # Generate 100 allocations
        generator.generate_allocations(1000)
        
        # Generate containers to fulfill demand + 5%
        generator.generate_containers()
        
        # Print summary
        generator.print_summary()
        
    except KeyboardInterrupt:
        print("\n\nProcess interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")


if __name__ == "__main__":
    main()
