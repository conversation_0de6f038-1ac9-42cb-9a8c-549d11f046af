# Large Scale Allocation and Container Generator

This Python script generates allocation data and containers for a warehouse management system. It creates allocations for customers and then generates containers to fulfill the demand plus a 5% buffer, processing data in chunks for large scale operations.

## Features

- Generates 100,000 cartons and associated allocations by default
- Processes data in configurable chunks (default: 100 cartons per chunk)
- Tracks total demand per SKU across all allocations
- Generates containers to fulfill demand + 5% buffer
- Makes HTTP POST requests to create allocations and containers
- Provides detailed progress tracking and summary reports
- Configurable parameters via config.py
- Handles interruptions gracefully with progress preservation

## Requirements

- Python 3.6+
- requests library

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

Run the script with default settings:
```bash
python allocation_generator.py
```

### Configuration

Modify `config.py` to adjust:
- API endpoint URL
- Number of allocations to generate
- Value ranges for wave, SKU, customers, etc.
- Request timeouts and delays

## API Endpoints

The script makes POST requests to these endpoints:

### Create Allocation
```
POST /ross/resources/v1.0/testingService/createAllocationForCustomer/{wave}/{sku}/{itemCount}/{customer}
```

### Create Container
```
POST /ross/resources/v1.0/testingService/createInboundCarton/0{loadUnit}/{itemCount}/{wave}/{sku}
```

## Data Generation Rules

### Allocations
- **Wave**: Random number between 1-1000 (same for all allocations in a run)
- **SKU**: 16-digit identifier starting with "4" (e.g., 4000002000000001)
- **Item Count**: Random number between 1-10 items per allocation
- **Customer**: Random customer ID between 1-100

### Containers
- **Load Unit**: 20-digit identifier starting with "02" (e.g., 02000000000000000001)
- **Item Count**: Random number between 1-5 items per container
- **Wave**: Same wave used for allocations
- **SKU**: Matches SKUs from allocations

## Output

The script provides:
- Real-time logging of allocation and container creation
- Success/failure status for each API call
- Summary report showing:
  - Total SKUs processed
  - Demand vs supply by SKU
  - Coverage percentage (should be ~105% due to 5% buffer)

## Error Handling

- Network timeouts and connection errors
- HTTP error responses
- Graceful handling of interrupted execution (Ctrl+C)

## Example Output

```
Allocation and Container Generator
============================================================
Generating 100 allocations for wave 542...
------------------------------------------------------------
✓ Created allocation: Wave=542, SKU=4123456789012345, Items=7, Customer=42
✓ Created allocation: Wave=542, SKU=4987654321098765, Items=3, Customer=15
...

Generating containers to fulfill demand + 5%...
------------------------------------------------------------
SKU 4123456789012345: Demand=7, Required=7
✓ Created container: LoadUnit=002000000000000000001, Items=4, SKU=4123456789012345
✓ Created container: LoadUnit=002000000000000000002, Items=3, SKU=4123456789012345
...

============================================================
SUMMARY
============================================================
Wave ID: 542
Total SKUs: 85
Total allocations demand: 485
Total container supply: 509

Demand vs Supply by SKU:
----------------------------------------
SKU 4123456789012345: Demand=  7, Supply=  7 (100.0%)
SKU 4987654321098765: Demand=  3, Supply=  4 (133.3%)
...
```
