#!/usr/bin/env python3
"""
Load Processor Script

This script reads container IDs from the global container file and sends them
to the playbook/load endpoint. It runs in parallel with the allocation generator.
"""

import requests
import json
import time
import random
import os
from typing import Set


class LoadProcessor:
    def __init__(self, base_url: str = "http://localhost:9001", container_file: str = "container_ids_global.txt"):
        self.base_url = base_url
        self.container_file = container_file
        self.processed_containers = set()
        self.processed_file = "processed_loads.txt"
        self.total_processed = 0
        
        # Load already processed containers
        self._load_processed_containers()
        
    def _load_processed_containers(self) -> None:
        """Load list of already processed container IDs"""
        if os.path.exists(self.processed_file):
            with open(self.processed_file, 'r') as f:
                for line in f:
                    container_id = line.strip()
                    if container_id and not container_id.startswith('#'):
                        self.processed_containers.add(container_id)
                        self.total_processed += 1
        
        print(f"Loaded {len(self.processed_containers)} previously processed containers")
    
    def _mark_as_processed(self, container_id: str) -> None:
        """Mark a container ID as processed"""
        with open(self.processed_file, 'a') as f:
            f.write(f"{container_id}\n")
            f.flush()
        self.processed_containers.add(container_id)
        self.total_processed += 1
    
    def _generate_load_data(self, container_id: str) -> dict:
        """Generate the JSON payload for the load endpoint"""
        return {
            "Id": container_id,
            "Type": "CASE",
            "Mission": {
                "Location": "CCTA10DP01"
            },
            "Data": {
                "Length": 1,
                "Width": 0.300,
                "Height": 0.500,
                "ProjectData": {
                    "Data": [
                        {"Key": "ItemCount", "Value": str(random.randint(1, 20))},
                        {"Key": "ItemCube", "Value": f"{random.uniform(0.1, 0.5):.3f}"},
                        {"Key": "ItemsToPick", "Value": str(random.randint(1, 10))}
                    ]
                }
            }
        }
    
    def send_load(self, container_id: str) -> bool:
        """Send a container load to the playbook endpoint"""
        url = f"{self.base_url}/playbook/load"
        payload = self._generate_load_data(container_id)
        
        try:
            response = requests.post(url, json=payload, timeout=10)
            if response.status_code in [200, 201, 202, 204]:  # 204 = No Content (success)
                print(f"✓ Sent load: {container_id}")
                return True
            else:
                print(f"✗ Failed to send load {container_id}: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error sending load {container_id}: {e}")
            return False
    
    def get_new_containers(self) -> list:
        """Read container file and return new container IDs"""
        new_containers = []
        
        if not os.path.exists(self.container_file):
            return new_containers
        
        try:
            with open(self.container_file, 'r') as f:
                for line in f:
                    container_id = line.strip()
                    # Skip comments and empty lines
                    if container_id and not container_id.startswith('#'):
                        if container_id not in self.processed_containers:
                            new_containers.append(container_id)
        except Exception as e:
            print(f"Error reading container file: {e}")
        
        return new_containers
    
    def process_loads(self, batch_size: int = 10, delay: float = 0.1) -> None:
        """Main processing loop"""
        print("Load Processor Started")
        print("=" * 60)
        print(f"Monitoring: {self.container_file}")
        print(f"Endpoint: {self.base_url}/playbook/load")
        print(f"Batch size: {batch_size}")
        print(f"Delay between requests: {delay}s")
        print("=" * 60)
        
        consecutive_empty_checks = 0
        max_empty_checks = 10  # Wait longer if no new containers
        
        while True:
            try:
                # Get new containers
                new_containers = self.get_new_containers()
                
                if new_containers:
                    consecutive_empty_checks = 0
                    print(f"\nFound {len(new_containers)} new containers to process")
                    
                    # Process in batches
                    for i in range(0, len(new_containers), batch_size):
                        batch = new_containers[i:i + batch_size]
                        successful = 0
                        
                        for container_id in batch:
                            if self.send_load(container_id):
                                self._mark_as_processed(container_id)
                                successful += 1
                            
                            # Small delay between requests
                            time.sleep(delay)
                        
                        print(f"Batch complete: {successful}/{len(batch)} successful")
                        print(f"Total processed: {self.total_processed:,}")
                        
                        # Small delay between batches
                        time.sleep(0.5)
                
                else:
                    consecutive_empty_checks += 1
                    if consecutive_empty_checks <= max_empty_checks:
                        print(f"No new containers found. Waiting... ({consecutive_empty_checks}/{max_empty_checks})")
                    else:
                        print("No new containers found. Waiting longer...")
                
                # Wait before checking again
                wait_time = 5 if consecutive_empty_checks <= max_empty_checks else 30
                time.sleep(wait_time)
                
            except KeyboardInterrupt:
                print(f"\n\n⚠️  Process interrupted by user")
                print(f"Total loads processed: {self.total_processed:,}")
                break
            except Exception as e:
                print(f"Unexpected error: {e}")
                time.sleep(10)  # Wait before retrying
    
    def print_status(self) -> None:
        """Print current processing status"""
        total_containers = 0
        if os.path.exists(self.container_file):
            with open(self.container_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.strip().startswith('#'):
                        total_containers += 1
        
        print(f"\n--- LOAD PROCESSOR STATUS ---")
        print(f"Total containers available: {total_containers:,}")
        print(f"Total loads processed: {self.total_processed:,}")
        print(f"Remaining to process: {total_containers - self.total_processed:,}")
        if total_containers > 0:
            progress = (self.total_processed / total_containers) * 100
            print(f"Progress: {progress:.1f}%")


def main():
    """Main function to run the load processor"""
    print("Container Load Processor")
    print("=" * 60)
    
    # Initialize the processor
    processor = LoadProcessor()
    
    try:
        # Show initial status
        processor.print_status()
        
        # Start processing
        processor.process_loads(batch_size=10, delay=0.1)
        
    except KeyboardInterrupt:
        print("\n\nProcess interrupted by user")
        processor.print_status()
    except Exception as e:
        print(f"\nUnexpected error: {e}")


if __name__ == "__main__":
    main()
