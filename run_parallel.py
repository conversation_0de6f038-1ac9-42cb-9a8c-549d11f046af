#!/usr/bin/env python3
"""
Parallel Runner Script

This script helps manage both the allocation generator and load processor
running in parallel.
"""

import subprocess
import time
import os
import signal
import sys


def check_process_status():
    """Check the status of both processes"""
    print("\n" + "=" * 80)
    print("PARALLEL PROCESSING STATUS")
    print("=" * 80)
    
    # Check container file
    container_file = "container_ids_global.txt"
    processed_file = "processed_loads.txt"
    
    total_containers = 0
    if os.path.exists(container_file):
        with open(container_file, 'r') as f:
            for line in f:
                if line.strip() and not line.strip().startswith('#'):
                    total_containers += 1
    
    total_processed = 0
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as f:
            for line in f:
                if line.strip() and not line.strip().startswith('#'):
                    total_processed += 1
    
    print(f"Container Generation:")
    print(f"  Total containers generated: {total_containers:,}")
    print(f"  Progress toward 5M target: {(total_containers/5000000)*100:.3f}%")
    
    print(f"\nLoad Processing:")
    print(f"  Total loads processed: {total_processed:,}")
    if total_containers > 0:
        processing_progress = (total_processed / total_containers) * 100
        print(f"  Processing progress: {processing_progress:.1f}%")
    
    print(f"\nFiles:")
    print(f"  Container IDs: {container_file}")
    print(f"  Processed loads: {processed_file}")


def main():
    """Main function to show status and provide instructions"""
    print("Parallel Processing Manager")
    print("=" * 80)
    print("This script helps monitor both processes running in parallel:")
    print("1. Allocation Generator (creating containers)")
    print("2. Load Processor (sending loads to playbook)")
    print("\nTo start both processes:")
    print("  Terminal 1: python allocation_generator.py")
    print("  Terminal 2: python load_processor.py")
    print("\nPress Ctrl+C to exit this monitor")
    
    try:
        while True:
            check_process_status()
            print(f"\nNext update in 60 seconds... (Ctrl+C to exit)")
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped.")
        check_process_status()


if __name__ == "__main__":
    main()
