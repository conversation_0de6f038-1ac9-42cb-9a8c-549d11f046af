"""
Configuration settings for the allocation generator
"""

# API Configuration
BASE_URL = "http://localhost:8080"
REQUEST_TIMEOUT = 10  # seconds
REQUEST_DELAY = 0.1   # seconds between requests

# Generation Parameters
NUM_ALLOCATIONS = 100
WAVE_MIN = 1
WAVE_MAX = 1000
ITEM_COUNT_MIN = 1
ITEM_COUNT_MAX = 10
CUSTOMER_MIN = 1
CUSTOMER_MAX = 100
CONTAINER_ITEMS_MIN = 1
CONTAINER_ITEMS_MAX = 5

# Supply buffer (5% extra)
SUPPLY_BUFFER_PERCENT = 0.05

# SKU Configuration
SKU_LENGTH = 16
SKU_PREFIX = "4"

# Load Unit Configuration  
LOAD_UNIT_LENGTH = 20
LOAD_UNIT_PREFIX = "02"
